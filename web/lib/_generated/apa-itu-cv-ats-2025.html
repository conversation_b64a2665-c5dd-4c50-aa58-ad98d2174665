<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta http-equiv="Content-Style-Type" content="text/css">
  <title></title>
  <meta name="Generator" content="Cocoa HTML Writer">
  <meta name="CocoaVersion" content="2575.7">
  <style type="text/css">
    p.p1 {margin: 0.0px 0.0px 4.0px 0.0px; text-align: justify; font: 11.0px Times}
    p.p2 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: justify; font: 12.0px Times}
    p.p3 {margin: 0.0px 0.0px 4.0px 0.0px; text-align: justify; font: 12.0px Times; min-height: 14.0px}
    p.p4 {margin: 12.0px 0.0px 12.0px 0.0px; text-align: justify; font: 12.0px Times}
    p.p11 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: center; font: 12.0px Times}
    p.p12 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: center; font: 12.0px 'Arial Unicode MS'}
    p.p13 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: justify; font: 12.0px Times; min-height: 14.0px}
    li.li5 {margin: 12.0px 0.0px 0.0px 0.0px; font: 12.0px Times}
    li.li6 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px Times}
    li.li7 {margin: 0.0px 0.0px 12.0px 0.0px; font: 12.0px Times}
    li.li8 {margin: 12.0px 0.0px 0.0px 0.0px; font: 12.0px 'Arial Unicode MS'}
    li.li9 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px 'Arial Unicode MS'}
    li.li10 {margin: 0.0px 0.0px 12.0px 0.0px; font: 12.0px 'Arial Unicode MS'}
    span.s1 {text-decoration: underline ; color: #1155cc}
    span.s2 {color: #188038}
    span.s3 {font: 12.0px 'Arial Unicode MS'}
    table.t1 {border-collapse: collapse}
    td.td1 {border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #bfbfbf #bfbfbf #bfbfbf #bfbfbf; padding: 0.0px 5.0px 0.0px 5.0px}
    ul.ul1 {list-style-type: disc}
  </style>
</head>
<body>
<p class="p1">Title: Apa Itu CV dan Bagaimana Cara Membuat CV yang Lolos ATS di 2025?</p>
<p class="p2">Slug: Apa-Itu-CV-Bagaimana-Cara-Membuat-CV-Lolos-ATS-2025?</p>
<p class="p2">Desc: Kamu sedang cari kerja, satu dokumen yang paling sering bikin galau pasti CV. Ada yang bilang CV harus kreatif biar standout, ada juga yang bilang cukup sederhana saja asal lolos sistem.</p>
<p class="p2">Keyword utama: membuat CV<span class="Apple-converted-space"> </span></p>
<p class="p2">keyrowd pendukung:<span class="Apple-converted-space">  </span>cv template google docs , template cv , cv ats, contoh cv , chat gpt cv , cv ai, buat cv cepat</p>
<p class="p3"><br></p>
<p class="p1">Apa Itu CV dan Bagaimana Cara Membuat CV yang Lolos ATS di 2025?</p>
<p class="p4">Kalau kamu sedang cari kerja, satu dokumen yang paling sering bikin galau pasti CV. Ada yang bilang CV harus kreatif biar standout, ada juga yang bilang cukup sederhana saja asal lolos sistem. Nah, faktanya, sekarang mayoritas perusahaan besar pakai Applicant Tracking System (ATS) untuk menyaring lamaran. Jadi percuma punya desain keren kalau CV kamu ditolak sistem bahkan sebelum dibaca HR.</p>
<p class="p4">Di artikel ini, kita akan bahas dari dasar: apa itu CV ATS, cara membuat CV yang benar, perbandingan template populer (Google Docs, template CV biasa), hingga bagaimana AI seperti ChatGPT dan <span class="s1">Gigsta Resume Builder</span> bisa membantu kamu bikin CV lebih cepat.</p>
<p class="p1">Apa Itu CV?</p>
<p class="p4">CV atau Curriculum Vitae adalah dokumen berisi ringkasan pengalaman, pendidikan, dan keahlian kamu. CV bukan sekadar daftar riwayat hidup, tapi juga “tiket masuk” ke meja HRD.</p>
<p class="p4">Bedanya dengan resume ala luar negeri, CV di Indonesia cenderung lebih panjang, bisa dua sampai tiga halaman. Tapi tren terbaru justru berbalik: perusahaan lebih suka CV yang singkat, jelas, dan bisa dibaca ATS.</p>
<p class="p1">Mengapa CV ATS Penting di 2025?</p>
<p class="p4">Coba bayangkan: kamu melamar di perusahaan besar, tapi ternyata ada 1.000 kandidat lain yang masuk hari itu juga. Mustahil HR baca satu per satu. Nah, di sinilah ATS dipakai.</p>
<p class="p4">ATS bekerja dengan cara memindai kata kunci, format, dan struktur CV. Jika cocok dengan kriteria lowongan, CV kamu lanjut. Kalau tidak, langsung gugur.</p>
<p class="p4">Menurut beberapa survei rekrutmen, lebih dari 90% perusahaan skala menengah hingga besar sudah menggunakan ATS. Jadi kalau CV kamu masih “kreatif” tapi tidak terstruktur, kemungkinan besar gagal di tahap awal.</p>
<p class="p1">Bagaimana Cara Membuat CV yang Lolos ATS?</p>
<p class="p4">Ada beberapa prinsip sederhana yang sering dilupakan pencari kerja:</p>
<ul class="ul1">
  <li class="li5">Format sederhana, jangan ribet<br>
<span class="Apple-converted-space"> </span>Hindari tabel, grafik, atau desain warna-warni. ATS hanya suka teks yang rapi.<br>
</li>
  <li class="li6">Kata kunci itu wajib<br>
<span class="Apple-converted-space"> </span>ATS mencari kesesuaian dengan job description. Kalau lowongan butuh “Digital Marketing Specialist”, pastikan kata itu muncul di pengalaman kamu.<br>
</li>
  <li class="li6">Struktur jelas<br>
</li>
  <ul class="ul1">
    <li class="li6">Identitas (nama, kontak).<br>
</li>
    <li class="li6">Ringkasan singkat.<br>
</li>
    <li class="li6">Pengalaman kerja.<br>
</li>
    <li class="li6">Pendidikan.<br>
</li>
    <li class="li6">Skill.<br>
</li>
  </ul>
  <li class="li7">Simpan dengan format yang benar<br>
<span class="Apple-converted-space"> </span>Paling aman: <span class="s2">.docx</span> atau PDF teks biasa. Jangan pakai PDF hasil scan.<br>
</li>
</ul>
<p class="p4">Kalau nggak mau ribet mikirin format, kamu bisa coba <span class="s1">Gigsta Resume Builder</span><span class="s3">. Tinggal isi data → AI langsung rapikan jadi CV ATS-Friendly.</span></p>
<p class="p1">Apa Kesalahan Umum Saat Membuat CV?</p>
<ul class="ul1">
  <li class="li8">Terlalu fokus desain (pakai ikon, tabel, warna) → ATS bingung bacanya.<br>
</li>
  <li class="li6">CV terlalu panjang tanpa isi yang relevan.<br>
</li>
  <li class="li9">Tidak menyesuaikan dengan lowongan → pakai CV sama untuk semua pekerjaan.<br>
</li>
  <li class="li7">File format aneh (misalnya JPG, atau PDF hasil screenshot).<br>
</li>
</ul>
<p class="p4">Banyak fresh graduate yang jatuh di sini. Jadi jangan ulangi kesalahan yang sama.</p>
<p class="p1">Apakah Ada Tools Membuat CV dengan AI?</p>
<p class="p4">Sekarang sudah ada banyak bantuan digital. Misalnya:</p>
<ul class="ul1">
  <li class="li8">ChatGPT untuk CV → bisa dipakai bikin ringkasan pengalaman atau menulis profil diri dengan bahasa lebih profesional.<br>
</li>
  <li class="li10">CV AI seperti Gigsta.io → bukan cuma menulis, tapi juga otomatis mengatur format, memastikan sesuai standar ATS, bahkan menyesuaikan dengan deskripsi pekerjaan yang kamu incar.<br>
</li>
</ul>
<p class="p4">Jadi, kamu bisa kombinasikan: pakai ChatGPT untuk ide, lalu rapikan dengan Gigsta Resume Builder biar hasilnya langsung siap kirim.</p>
<p class="p1">CV Template Google Docs: Masih Oke atau Sudah Ketinggalan?</p>
<p class="p4">Banyak orang masih suka pakai CV template Google Docs. Wajar sih, gratis dan gampang dipakai. Tapi ada plus minusnya:</p>
<p class="p4">Kelebihan:</p>
<ul class="ul1">
  <li class="li5">Gratis, tinggal pilih template.<br>
</li>
  <li class="li7">Bisa edit bareng kalau mau dibantu teman.<br>
</li>
</ul>
<p class="p4">Kekurangan:</p>
<ul class="ul1">
  <li class="li8">Banyak template pakai tabel/kolom → tidak ramah ATS.<br>
</li>
  <li class="li7">Tidak ada fitur otomatis menyesuaikan dengan job description.<br>
</li>
</ul>
<p class="p4">Jadi, Google Docs cocok untuk sekadar bikin draft. Tapi kalau kamu serius mau apply ke perusahaan besar, sebaiknya upgrade ke template ATS atau pakai builder AI.</p>
<p class="p1">Template CV yang Sering Dipakai Pencari Kerja</p>
<ul class="ul1">
  <li class="li8">Template CV ATS-Friendly → simple, 1 kolom, fokus konten.<br>
</li>
  <li class="li9">Template CV Profesional → ada ringkasan, pengalaman, pendidikan, skill.<br>
</li>
  <li class="li9">Template CV Kreatif → pakai warna, ikon, desain (cocok industri kreatif tapi tidak untuk ATS).<br>
</li>
  <li class="li10">Template CV Google Docs → gratis tapi perlu hati-hati dengan struktur.<br>
</li>
</ul>
<p class="p1">CV ATS vs CV Kreatif</p>
<table cellspacing="0" cellpadding="0" class="t1">
  <tbody>
    <tr>
      <td valign="middle" class="td1">
        <p class="p11">Aspek</p>
      </td>
      <td valign="middle" class="td1">
        <p class="p11">CV ATS-Friendly</p>
      </td>
      <td valign="middle" class="td1">
        <p class="p11">CV Kreatif</p>
      </td>
    </tr>
    <tr>
      <td valign="middle" class="td1">
        <p class="p11">Desain</p>
      </td>
      <td valign="middle" class="td1">
        <p class="p11">Sederhana, 1 kolom</p>
      </td>
      <td valign="middle" class="td1">
        <p class="p11">Warna, tabel, ikon</p>
      </td>
    </tr>
    <tr>
      <td valign="middle" class="td1">
        <p class="p11">Font</p>
      </td>
      <td valign="middle" class="td1">
        <p class="p11">Arial / Calibri</p>
      </td>
      <td valign="middle" class="td1">
        <p class="p11">Bebas, kadang dekoratif</p>
      </td>
    </tr>
    <tr>
      <td valign="middle" class="td1">
        <p class="p11">Format</p>
      </td>
      <td valign="middle" class="td1">
        <p class="p11">.docx / PDF teks</p>
      </td>
      <td valign="middle" class="td1">
        <p class="p11">PDF gambar / InDesign</p>
      </td>
    </tr>
    <tr>
      <td valign="middle" class="td1">
        <p class="p11">ATS Friendly</p>
      </td>
      <td valign="middle" class="td1">
        <p class="p12">✅ Ya</p>
      </td>
      <td valign="middle" class="td1">
        <p class="p12">❌ Tidak</p>
      </td>
    </tr>
    <tr>
      <td valign="middle" class="td1">
        <p class="p11">Cocok untuk</p>
      </td>
      <td valign="middle" class="td1">
        <p class="p11">Semua industri</p>
      </td>
      <td valign="middle" class="td1">
        <p class="p11">Desain, seni, iklan</p>
      </td>
    </tr>
  </tbody>
</table>
<p class="p13"><br></p>
<p class="p1">ChatGPT CV vs CV AI</p>
<ul class="ul1">
  <li class="li5">ChatGPT CV: bagus untuk membantu menulis, tapi hasilnya tetap perlu kamu format manual.<br>
</li>
  <li class="li7">CV AI (Gigsta.io): langsung beres. Bukan hanya teks, tapi format ATS-nya sudah otomatis benar. Bahkan bisa disesuaikan dengan lowongan spesifik yang kamu incar.<br>
</li>
</ul>
<p class="p4">Intinya: kalau mau cepat dan praktis, pakai CV AI. Kalau masih mau eksperimen sendiri, ChatGPT bisa jadi asisten menulis.</p>
<p class="p4">Membuat CV di 2025 tidak bisa sembarangan. ATS sudah jadi gerbang awal di hampir semua perusahaan. Jadi, kuncinya:</p>
<ul class="ul1">
  <li class="li5">Pakai format sederhana.<br>
</li>
  <li class="li6">Masukkan kata kunci dari lowongan.<br>
</li>
  <li class="li6">Jangan terlalu desain-oriented.<br>
</li>
  <li class="li7">Gunakan bantuan AI bila perlu.<br>
</li>
</ul>
<p class="p4">Kalau kamu ingin cepat, praktis, dan pasti sesuai standar ATS, langsung saja coba <span class="s1">Gigsta Resume Builder</span>.</p>
<p class="p4">Mulai sekarang, jangan biarkan CV jadi penghalang karier kamu.<br>
Buat CV ATS-Friendly gratis di <span class="s1">Gigsta.io</span> hanya dalam beberapa menit!</p>
<p class="p13"><br></p>
<p class="p13"><br></p>
<p class="p2">FAQ Schema Markup<span class="Apple-converted-space"> </span></p>
<p class="p13"><br></p>
<p class="p2">{</p>
<p class="p2"><span class="Apple-converted-space">  </span>"@context": "https://schema.org",</p>
<p class="p2"><span class="Apple-converted-space">  </span>"@type": "FAQPage",</p>
<p class="p2"><span class="Apple-converted-space">  </span>"mainEntity": [{</p>
<p class="p2"><span class="Apple-converted-space">    </span>"@type": "Question",</p>
<p class="p2"><span class="Apple-converted-space">    </span>"name": "Apa itu CV ATS-Friendly?",</p>
<p class="p2"><span class="Apple-converted-space">    </span>"acceptedAnswer": {</p>
<p class="p2"><span class="Apple-converted-space">      </span>"@type": "Answer",</p>
<p class="p2"><span class="Apple-converted-space">      </span>"text": "CV ATS-Friendly adalah CV yang dirancang agar mudah dibaca sistem Applicant Tracking System dengan format sederhana, font standar, dan kata kunci relevan."</p>
<p class="p2"><span class="Apple-converted-space">    </span>}</p>
<p class="p2"><span class="Apple-converted-space">  </span>},</p>
<p class="p2"><span class="Apple-converted-space">  </span>{</p>
<p class="p2"><span class="Apple-converted-space">    </span>"@type": "Question",</p>
<p class="p2"><span class="Apple-converted-space">    </span>"name": "Bagaimana cara membuat CV ATS-Friendly?",</p>
<p class="p2"><span class="Apple-converted-space">    </span>"acceptedAnswer": {</p>
<p class="p2"><span class="Apple-converted-space">      </span>"@type": "Answer",</p>
<p class="p2"><span class="Apple-converted-space">      </span>"text": "Gunakan format sederhana, sertakan kata kunci dari job description, susun struktur jelas, dan simpan file dalam format .docx atau PDF standar."</p>
<p class="p2"><span class="Apple-converted-space">    </span>}</p>
<p class="p2"><span class="Apple-converted-space">  </span>},</p>
<p class="p2"><span class="Apple-converted-space">  </span>{</p>
<p class="p2"><span class="Apple-converted-space">    </span>"@type": "Question",</p>
<p class="p2"><span class="Apple-converted-space">    </span>"name": "Apakah ada tools membuat CV dengan AI?",</p>
<p class="p2"><span class="Apple-converted-space">    </span>"acceptedAnswer": {</p>
<p class="p2"><span class="Apple-converted-space">      </span>"@type": "Answer",</p>
<p class="p2"><span class="Apple-converted-space">      </span>"text": "Ya, salah satunya adalah Gigsta.io Resume Builder. Dengan bantuan AI, kamu bisa membuat CV ATS-Friendly otomatis sesuai job description hanya dalam hitungan menit."</p>
<p class="p2"><span class="Apple-converted-space">    </span>}</p>
<p class="p2"><span class="Apple-converted-space">  </span>}]</p>
<p class="p2">}</p>
<p class="p13"><br></p>
<p class="p13"><br></p>
</body>
</html>
